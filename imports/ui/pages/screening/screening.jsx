import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withRouter } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";

import NewScreening from "./new-screening.jsx";
import ScreeningResults from "./screening-results.jsx";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import { Loading } from "../../components/loading.jsx";
import * as utils from "/imports/api/utilities/utilities";
import getCurrentBMPScreeningStatus from "/imports/api/helpers/getCurrentBMPScreeningStatus";
import ScrollIndicator from "../../components/scrollIndicator";
import ScrollIndicatorView from "../../components/scrollIndicatorView";
import FirstScreening from "./first-screening";
import ScreeningDashboard from "./screening-dashboard";
import BenchmarkPeriodHelpers from "/imports/api/benchmarkPeriods/methods";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";

export function getBenchmarkPeriodLabel(benchmarkPeriodId) {
  return utils.capitalizeFirstLetter(utils.translateBenchmarkPeriod(benchmarkPeriodId).name);
}

export function startNewClasswide(studentGroup, history) {
  const { siteId, orgid, grade } = studentGroup;
  const studentGroupId = studentGroup._id;
  const benchmarkPeriod = BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
  const opts = {
    studentGroupId,
    siteId,
    benchmarkPeriodId: grade === "HS" ? "allPeriods" : benchmarkPeriod && benchmarkPeriod._id
  };
  Meteor.call("startClasswideIntervention", opts, error => {
    if (error) {
      utils.ninjalog.error({
        msg: `error setting up new screening period: ${error}`
      });
    } else {
      history.push(`/site/${siteId}/student-groups/${studentGroupId}/${utils.dashboardNavs.classwide}`);
    }
  });
}

class Screening extends Component {
  static contextType = SchoolYearContext;

  constructor(props) {
    super(props);

    this.startNewScreening = this.startNewScreening.bind(this);
    this.state = {
      currentBMPScreeningStatus: null,
      previousIncompleteScreenings: [],
      scroll: {
        lastReload: new Date().valueOf()
      }
    };
  }

  refreshScroll = () => {
    const state = { ...this.state };
    state.scroll.lastReload = new Date().valueOf();
    if (this.mounted) this.setState(state);
  };

  componentDidMount() {
    this.mounted = true;
    if (this.props.studentGroup && !this.state.currentBMPScreeningStatus) {
      getCurrentBMPScreeningStatus(this.props.studentGroup, (err, resp) => {
        if (this.mounted) {
          this.setState({
            currentBMPScreeningStatus: resp
          });
        }
      });
      this.getPreviousIncompleteScreeningBenchmarkPeriodIds(this.props.studentGroup._id, this.props.studentGroup.grade);
    }
  }

  componentWillUnmount() {
    this.mounted = false;
  }

  getPreviousIncompleteScreeningBenchmarkPeriodIds(studentGroupId, grade) {
    Meteor.call(
      "ScreeningHelpers:getPreviousIncompleteScreeningBenchmarkPeriodIds",
      studentGroupId,
      grade,
      (err, resp) => {
        if (err) {
          Alert.error("Error fetching incomplete screenings");
        } else if (this.mounted) {
          this.setState({
            previousIncompleteScreenings: resp
          });
        }
      }
    );
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(newProps) {
    if ((this.props.studentGroup || newProps.studentGroup) && !this.state.currentBMPScreeningStatus) {
      getCurrentBMPScreeningStatus(newProps.studentGroup, (err, resp) => {
        if (this.mounted) {
          this.setState({
            currentBMPScreeningStatus: resp
          });
        }
      });
      this.getPreviousIncompleteScreeningBenchmarkPeriodIds(newProps.studentGroup._id, newProps.studentGroup.grade);
    }
  }

  startNewScreening() {
    const { siteId } = this.props.studentGroup;
    const { grade } = this.props.studentGroup;
    const { orgid } = this.props.studentGroup;
    const studentGroupId = this.props.studentGroup._id;
    const opts = {
      studentGroupId,
      siteId,
      grade,
      orgid
    };
    Meteor.call("setUpNewScreeningPeriod", opts, (error, results) => {
      if (error) {
        utils.ninjalog.error({
          msg: `error setting up new screening period: ${error}`
        });
      }
      if (results) {
        this.props.history.push(
          `/site/${siteId}/student-groups/${studentGroupId}/${utils.dashboardNavs.screening}/form`
        );
      }
    });
  }

  formatCurrentPeriod = () => {
    if (!this.state.currentBMPScreeningStatus) return null;
    return utils.capitalizeFirstLetter(
      utils.translateBenchmarkPeriod(this.state.currentBMPScreeningStatus.periodId).name
    );
  };

  formatScreeningTitle = (currentPeriod = this.formatCurrentPeriod()) => {
    return `${currentPeriod} ${utils.getFormattedSchoolYear(
      (this.state.currentBMPScreeningStatus && this.state.currentBMPScreeningStatus.schoolYear) ||
        this.context.schoolYear
    )} screening`;
  };

  render() {
    const {
      studentGroup,
      inActiveSchoolYear,
      activeComponent,
      assessmentResultId,
      isReadOnly,
      screeningBenchmarkPeriodId
    } = this.props;
    const hasScreeningHistory = studentGroup.history && studentGroup.history.some(h => h.type === "benchmark");
    const { currentBMPScreeningStatus } = this.state;

    if (this.props.loading || !currentBMPScreeningStatus) {
      return <Loading />;
    }

    return (
      <React.Fragment>
        <div data-testid="screeningContent" />
        {this.renderScreeningComponent(
          activeComponent,
          studentGroup,
          inActiveSchoolYear,
          screeningBenchmarkPeriodId,
          currentBMPScreeningStatus,
          assessmentResultId,
          isReadOnly,
          hasScreeningHistory
        )}
      </React.Fragment>
    );
  }

  renderScreeningComponent(
    activeComponent,
    studentGroup,
    inActiveSchoolYear,
    screeningBenchmarkPeriodId,
    currentBMPScreeningStatus,
    assessmentResultId,
    isReadOnly,
    hasScreeningHistory
  ) {
    if (activeComponent === "ScreeningForm") {
      return (
        <ScrollIndicator
          container={this}
          targetSelector={"div"}
          indicatorComponent={<ScrollIndicatorView />}
          uniqKey={this.state.scroll.lastReload}
          wait={this.props.loading}
        >
          <NewScreening
            studentGroupId={studentGroup._id}
            inActiveSchoolYear={inActiveSchoolYear}
            isReadOnly={this.props.isReadOnly}
            refreshScroll={this.refreshScroll}
            screeningBenchmarkPeriodId={screeningBenchmarkPeriodId || currentBMPScreeningStatus.periodId}
          />
        </ScrollIndicator>
      );
    }
    if (activeComponent === "ScreeningResults" && assessmentResultId) {
      return (
        <ScrollIndicator
          container={this}
          targetSelector={"div"}
          indicatorComponent={<ScrollIndicatorView />}
          uniqKey={this.state.scroll.lastReload}
          wait={this.props.loading}
        >
          <ScreeningResults
            assessmentResultId={assessmentResultId}
            studentGroupId={studentGroup._id}
            siteId={studentGroup.siteId}
            students={this.props.students}
            refreshScroll={this.refreshScroll}
            orgid={studentGroup.orgid}
          />
        </ScrollIndicator>
      );
    }
    const canManageScreenings = inActiveSchoolYear && !isReadOnly;
    const shouldSeeScreeningSummary =
      hasScreeningHistory ||
      this.state.previousIncompleteScreenings.length ||
      (currentBMPScreeningStatus.hasScreening && !currentBMPScreeningStatus.completed);
    if (shouldSeeScreeningSummary) {
      return (
        <ScrollIndicator
          container={this}
          targetSelector={"div"}
          indicatorComponent={<ScrollIndicatorView />}
          uniqKey={this.props.activeComponent}
          wait={this.props.loading}
        >
          <ScreeningDashboard
            canManageScreenings={canManageScreenings}
            currentBMPScreeningStatus={currentBMPScreeningStatus}
            formatScreeningTitle={this.formatScreeningTitle}
            startNewScreening={this.startNewScreening}
            studentGroup={studentGroup}
            previousIncompleteScreenings={this.state.previousIncompleteScreenings}
            benchmarkPeriods={this.props.benchmarkPeriods}
          />
        </ScrollIndicator>
      );
    }
    return (
      <FirstScreening
        studentGroup={this.props.studentGroup}
        inActiveSchoolYear={inActiveSchoolYear}
        startNewClasswide={() => startNewClasswide(this.props.studentGroup, this.props.history)}
        canManageScreenings={canManageScreenings}
        startNewScreening={this.startNewScreening}
      />
    );
  }
}

Screening.propTypes = {
  activeComponent: PropTypes.string,
  assessmentResultId: PropTypes.string,
  benchmarkPeriods: PropTypes.array,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  loading: PropTypes.bool,
  studentGroup: PropTypes.object,
  students: PropTypes.array,
  user: PropTypes.object,
  screeningBenchmarkPeriodId: PropTypes.string,
  history: PropTypes.object
};

const ScreeningWithRouter = withRouter(Screening);

// Data Container
export default withTracker(({ studentGroup }) => {
  const user = getMeteorUserSync();
  const benchmarkPeriodsSub = Meteor.subscribe("BenchmarkPeriods");
  const screeningAssignmentsSub = Meteor.subscribe("ScreeningAssignments");
  const studentGroupEnrollmentsSub = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroup._id);
  const bmwSub = Meteor.subscribe("BenchmarkWindowsForStudentGroupAndPeriod", studentGroup._id);

  const loading =
    !benchmarkPeriodsSub.ready() ||
    !screeningAssignmentsSub.ready() ||
    !studentGroupEnrollmentsSub.ready() ||
    !bmwSub.ready();

  const studentGroups = StudentGroups.find().fetch();
  const benchmarkPeriods = BenchmarkPeriods.find().fetch();

  // These are the props that will get passed
  return {
    benchmarkPeriods,
    studentGroup,
    studentGroups,
    loading,
    user
  };
})(ScreeningWithRouter);

export { ScreeningWithRouter as PureScreening }; // for testing
