import React from "react";
import { Switch } from "react-router-dom";
import PropTypes from "prop-types";
import Layout from "../layouts/layout";
import CustomRoute from "./CustomRoute";
import {
  checkIsTeacherOrAdminOrSupportOrUniversalCoach,
  checkLoggedIn,
  setApplicationVersion
} from "../../startup/client/routeUtils";
import SideNavLayout from "../layouts/side-nav-layout-wrapper";
import Dashboard from "../pages/dashboard/dashboard";
import StudentDetail from "../pages/student-detail/student-detail";
import PrintLayout from "../layouts/print-layout";
import * as utils from "/imports/api/utilities/utilities";
import { AppDataContext } from "./AppDataContext";
import NoEnrollments from "../pages/no-enrollments/no-enrollments";
import { navbarItemsByRoleId } from "./navbarItems";
import NotAssignedTeacherPage from "../pages/student-groups/not-assigned-teacher-page";

export default class TeacherRoutes extends React.Component {
  navbarItems = navbarItemsByRoleId.arbitraryIdteacher;

  routerGroupName = "teachers";

  render() {
    return (
      <Switch>
        <CustomRoute
          path="/site/:siteId/student-groups"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => <Layout content={<NotAssignedTeacherPage />} />}
        />
        <CustomRoute
          path="/site/:siteId/student-groups/:studentGroupId"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId } = match.params;
            return (
              <SideNavLayout
                content={<Dashboard siteId={siteId} studentGroupId={studentGroupId} activeNavName={null} />}
                siteId={siteId}
                studentGroupId={studentGroupId}
              />
            );
          }}
        />
        <CustomRoute
          path="/site/:siteId/student-groups/:studentGroupId/students/:studentId/:section?"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId, studentId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear, orgid }) => (
                  <SideNavLayout
                    content={
                      <StudentDetail
                        siteId={siteId}
                        studentGroupId={studentGroupId}
                        studentId={studentId}
                        activeNavName={null}
                        schoolYear={schoolYear}
                      />
                    }
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    schoolYear={schoolYear}
                    studentId={studentId}
                    context="student-detail"
                    orgid={orgid}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path="/print/:component"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { component } = match.params;
            return <PrintLayout context="print-component" component={component} />;
          }}
        />
        <CustomRoute
          path={`/site/:siteId/student-groups/:studentGroupId/${utils.dashboardNavs.classwide}`}
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear, orgid }) => (
                  <SideNavLayout
                    content={
                      <Dashboard
                        siteId={siteId}
                        studentGroupId={studentGroupId}
                        activeNavName={utils.dashboardNavs.classwide}
                        schoolYear={schoolYear}
                        orgid={orgid}
                      />
                    }
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    schoolYear={schoolYear}
                    orgid={orgid}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path={`/site/:siteId/student-groups/:studentGroupId/${utils.dashboardNavs.individual}`}
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear, orgid }) => (
                  <SideNavLayout
                    content={
                      <Dashboard
                        siteId={siteId}
                        studentGroupId={studentGroupId}
                        activeNavName={utils.dashboardNavs.individual}
                        schoolYear={schoolYear}
                        orgid={orgid}
                      />
                    }
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    schoolYear={schoolYear}
                    orgid={orgid}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path={`/site/:siteId/student-groups/:studentGroupId/${utils.dashboardNavs.screening}`}
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear, orgid }) => (
                  <SideNavLayout
                    content={
                      <Dashboard
                        orgid={orgid}
                        siteId={siteId}
                        studentGroupId={studentGroupId}
                        activeNavName={utils.dashboardNavs.screening}
                        activeScreeningComponent={null}
                        schoolYear={schoolYear}
                      />
                    }
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    schoolYear={schoolYear}
                    orgid={orgid}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path={`/site/:siteId/student-groups/:studentGroupId/${utils.dashboardNavs.screening}/form/:screeningBenchmarkPeriodId?`}
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId, screeningBenchmarkPeriodId } = match.params;
            return (
              <SideNavLayout
                content={
                  <Dashboard
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    activeNavName={utils.dashboardNavs.screening}
                    activeScreeningComponent="ScreeningForm"
                    screeningBenchmarkPeriodId={screeningBenchmarkPeriodId}
                  />
                }
                siteId={siteId}
                studentGroupId={studentGroupId}
              />
            );
          }}
        />
        <CustomRoute
          path={`/site/:siteId/student-groups/:studentGroupId/${utils.dashboardNavs.screening}/results/:assessmentResultId`}
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId, assessmentResultId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear, orgid }) => (
                  <SideNavLayout
                    content={
                      <Dashboard
                        orgid={orgid}
                        siteId={siteId}
                        studentGroupId={studentGroupId}
                        activeNavName={utils.dashboardNavs.screening}
                        activeScreeningComponent="ScreeningResults"
                        assessmentResultId={assessmentResultId}
                        schoolYear={schoolYear}
                      />
                    }
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    schoolYear={schoolYear}
                    orgid={orgid}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path={`/site/:siteId/student-groups/:studentGroupId/${utils.dashboardNavs.students}`}
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear, orgid }) => (
                  <SideNavLayout
                    content={
                      <Dashboard
                        orgid={orgid}
                        siteId={siteId}
                        studentGroupId={studentGroupId}
                        activeNavName={utils.dashboardNavs.students}
                        schoolYear={schoolYear}
                      />
                    }
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    schoolYear={schoolYear}
                    orgid={orgid}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path="/site/:siteId/student-groups/:studentGroupId/growth"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear, orgid }) => (
                  <SideNavLayout
                    content={
                      <Dashboard
                        orgid={orgid}
                        siteId={siteId}
                        studentGroupId={studentGroupId}
                        activeNavName={utils.dashboardNavs.growth}
                        schoolYear={schoolYear}
                      />
                    }
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    schoolYear={schoolYear}
                    orgid={orgid}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path="/site/:siteId/student-groups/:studentGroupId/interventionWithoutScreening"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, studentGroupId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear, orgid }) => (
                  <SideNavLayout
                    content={
                      <Dashboard
                        siteId={siteId}
                        studentGroupId={studentGroupId}
                        activeNavName={utils.dashboardNavs.interventionWithoutScreening}
                        schoolYear={schoolYear}
                      />
                    }
                    siteId={siteId}
                    studentGroupId={studentGroupId}
                    schoolYear={schoolYear}
                    orgid={orgid}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path="/:orgid/site/:siteId/no-enrollments"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            return <Layout content={<NoEnrollments {...match.params} />} />;
          }}
        />
      </Switch>
    );
  }
}

TeacherRoutes.defaultProps = {
  tasks: [checkLoggedIn, setApplicationVersion, checkIsTeacherOrAdminOrSupportOrUniversalCoach]
};

TeacherRoutes.propTypes = {
  tasks: PropTypes.array,
  siteId: PropTypes.string
};
