import get from "lodash/get";

import { Users } from "../../users/users.js";
import { Sites } from "../../sites/sites";
import { isUniversalCoach, roles } from "../../roles/methods.js";

async function checkAnyRoleAsync(items, checkFn) {
  if (items.length === 0) {
    return false;
  }

  return new Promise((resolve, reject) => {
    let completedCount = 0;
    let resolved = false;

    const handleResult = (result, error = null) => {
      if (resolved) return; // Prevent multiple resolutions

      if (error) {
        resolved = true;
        reject(error);
        return;
      }

      if (result === true) {
        resolved = true;
        resolve(true);
        return;
      }

      completedCount++;
      if (completedCount === items.length) {
        resolved = true;
        resolve(false);
      }
    };

    // Start all checks in parallel
    items.forEach(async item => {
      try {
        const result = await checkFn(item);
        handleResult(result);
      } catch (error) {
        handleResult(null, error);
      }
    });
  });
}

export function getSiteAccess(user) {
  return get(user, "profile.siteAccess", []);
}

export async function hasSiteAccess(userId, siteId) {
  if (isUniversalCoach(userId)) {
    return true;
  }
  const curUser = await Users.findOneAsync({ _id: userId });
  const siteAccessList = getSiteAccess(curUser);
  const accessToSite = siteAccessList.find(
    siteAccess => siteAccess.siteId === siteId && siteAccess.role !== "arbitraryIdsupport"
  );
  return !!accessToSite;
}

export function hasRoleInSiteAccess(siteAccesses, roleName) {
  let result = false;
  // We're using the roles array rather than the db for performance reasons
  const role = roles.find(r => r.name === roleName);
  if (role && siteAccesses) {
    result = !!siteAccesses.find(sa => sa.role === role._id);
  }
  return result;
}

export function getSupportUserAccess(supportUser) {
  return get(supportUser, "profile.organizationAccess", []);
}

export async function hasAccess(
  roleNames = [],
  { userId, user: optionalUser, siteId, orgid, shouldCheckOrgAccessForSupport = true }
) {
  const user = optionalUser || (await Users.findOneAsync({ _id: userId }));

  const siteAccess = getSiteAccess(user);

  const checkRole = async roleName => {
    if (roleName === "dataAdmin" && user?.profile?.orgid !== orgid) {
      return false;
    }

    if (roleName === "admin") {
      if (!siteId) {
        return false;
      }
      const filteredSiteAccess = siteAccess.filter(sa => sa.siteId === siteId);
      return hasRoleInSiteAccess(filteredSiteAccess, roleName);
    }

    if (roleName === "support") {
      if (!shouldCheckOrgAccessForSupport) {
        return hasRoleInSiteAccess(siteAccess, roleName);
      }
      if (!orgid && !siteId) {
        return false;
      }
      const orgidToUse = orgid || (await Sites.findOneAsync({ _id: siteId }, { fields: { orgid: 1 } }))?.orgid || "";
      return getSupportUserAccess(user).includes(orgidToUse);
    }

    return hasRoleInSiteAccess(siteAccess, roleName);
  };

  return checkAnyRoleAsync(roleNames, checkRole);
}
