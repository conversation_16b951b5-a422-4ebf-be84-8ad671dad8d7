import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useTracker } from "meteor/react-meteor-data";
import { ListGroup } from "react-bootstrap";

import { Grades } from "/imports/api/grades/grades";
import * as utils from "/imports/api/utilities/utilities";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";
import { Loading } from "./loading.jsx";
import { areSubscriptionsLoading } from "../utilities";
import { getUserRoles } from "../pages/data-admin/utilities";
import { SchoolYearContext } from "../../contexts/SchoolYearContext";
import { OrganizationContext } from "../../contexts/OrganizationContext";
import { SiteContext } from "../../contexts/SiteContext";

const AdminSideNav = ({ loading, siteName, siteId, orgid, grades, organizationName, match }) => {
  const [shouldDisplayZendeskWidget, setShouldDisplayZendeskWidget] = useState(false);
  const [isFetchingZendeskData, setIsFetchingZendeskData] = useState(true);

  // Handle Zendesk widget flag fetching
  useEffect(() => {
    const userRole = getUserRoles();
    const isAllowedToFetchZendeskFlag = userRole.includes("teacher") || userRole.includes("admin");
    if (!isAllowedToFetchZendeskFlag) {
      return;
    }
    Meteor.call("Settings:getZendeskWidgetFlag", siteId, (err, resp) => {
      if (!err) {
        setShouldDisplayZendeskWidget(resp);
      }
    });
  }, [siteId]);

  // Handle fetching data completion
  useEffect(() => {
    if (siteName && organizationName) {
      setIsFetchingZendeskData(false);
    }
  }, [siteName, organizationName]);

  const renderZendeskWidget = () => {
    if (!isFetchingZendeskData && shouldDisplayZendeskWidget) {
      return utils.renderZendeskWidget(organizationName, siteName);
    }
    return null;
  };

  const isActive = gradeId => (match.params.gradeId === gradeId ? " active" : "");

  if (loading) {
    // TODO(fmazur) - remove message
    return <Loading message="side nav admin" />;
  }

  return (
    <aside className="side-nav">
      <div className="site-selector" data-testid="siteSelectorId">
        {siteName}
      </div>
      <ListGroup className="student-grade-list student-group-list">
        <Link key="all" to={`/school-overview/${orgid}/all/${siteId}`} className={`${isActive("all")} list-group-item`}>
          School Overview
        </Link>
        {grades.map(({ _id, display }) => (
          <Link
            key={_id}
            to={`/school-overview/${orgid}/${_id}/${siteId}`}
            className={`${isActive(_id)} list-group-item`}
          >
            {getCurrentEnrolledGrade(display)}
          </Link>
        ))}
      </ListGroup>
      {renderZendeskWidget()}
    </aside>
  );
};

AdminSideNav.propTypes = {
  grades: PropTypes.array,
  loading: PropTypes.bool,
  organizationName: PropTypes.string,
  siteName: PropTypes.node,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  history: PropTypes.object,
  match: PropTypes.object
};

const AdminSideNavWithTracker = ({ orgid, siteId, history, ...props }) => {
  const { schoolYear } = useContext(SchoolYearContext);
  const { org } = useContext(OrganizationContext);
  const { site } = useContext(SiteContext);

  const trackerData = useTracker(() => {
    if (!schoolYear) {
      return { loading: true };
    }

    const gradesSub = Meteor.subscribe("GradesWithStudentGroupsInSite", siteId, schoolYear);
    // FIXME(fmazur) - may break other components, potentially restore for now
    const userDataSub = Meteor.subscribe("userData");

    const loading = areSubscriptionsLoading(gradesSub, userDataSub);

    let grades = [];

    if (!loading) {
      // TODO(fmazur) - handle this elsewhere
      // if (!site) {
      //   const user = Meteor.user();
      //   history.push(user ? "/unauthorized" : "/login");
      // }
      grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
    }

    return {
      loading,
      grades,
      organizationName: org?.name,
      siteName: site?.name,
      orgid,
      siteId
    };
  }, [schoolYear, siteId, orgid, history]);

  return <AdminSideNav {...props} {...trackerData} />;
};

AdminSideNavWithTracker.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  history: PropTypes.object,
  schoolYear: PropTypes.number
};

export default withRouter(AdminSideNavWithTracker);
